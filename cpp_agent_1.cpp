/*
 * MCTS Agent for Water Fight Game (SoakOverflow)
 *
 * This agent uses Monte Carlo Tree Search to find optimal moves.
 *
 * Features:
 * - High-precision timer for staying under 50ms time limit
 * - Configurable time cutoff (default 46ms for safety margin)
 * - Territory-based scoring with wetness concentration preferences
 * - Prefers eliminating agents over spreading wetness
 *
 * Usage:
 * - Compile: g++ -O2 -std=c++17 cpp_agent_1.cpp -o cpp_agent_1
 * - Run: ./cpp_agent_1 [time_limit_ms]
 * - Example: ./cpp_agent_1 45.0  (sets time limit to 45ms)
 */

#include <iostream>
#include <vector>
#include <string>
#include <sstream>
#include <algorithm>
#include <random>
#include <chrono>
#include <cmath>
#include <cstdlib>
#include <map>
#include <set>
#include <queue>
#include <memory>
#include <unordered_map>
#include <unordered_set>
#include <limits>
#include <climits>
#include <exception>

using namespace std;
using namespace std::chrono;

// Configuration constants
const int MAX_WETNESS = 100;
const int SPLASH_DAMAGE = 30;
const int MAX_THROW_RANGE = 4;
const int MAX_POINT_DIFF = 600;
const int MAX_TURNS = 100;

// Configurable time management
double TIME_LIMIT_MS = 46.0; // Configurable cutoff time in milliseconds

// High precision timer class
class Timer {
private:
    high_resolution_clock::time_point start_time;
    
public:
    void start() {
        start_time = high_resolution_clock::now();
    }
    
    double elapsed_ms() const {
        auto now = high_resolution_clock::now();
        auto duration = duration_cast<microseconds>(now - start_time);
        return duration.count() / 1000.0; // Convert to milliseconds
    }
    
    bool time_up() const {
        return elapsed_ms() >= TIME_LIMIT_MS;
    }
};

// Position structure
struct Pos {
    int x, y;
    
    Pos() : x(0), y(0) {}
    Pos(int x, int y) : x(x), y(y) {}
    
    bool operator==(const Pos& other) const {
        return x == other.x && y == other.y;
    }
    
    bool operator!=(const Pos& other) const {
        return !(*this == other);
    }
    
    int manhattan_distance(const Pos& other) const {
        return abs(x - other.x) + abs(y - other.y);
    }
};

// Agent structure
struct Agent {
    int id;
    int player;
    Pos pos;
    int cooldown;
    int splash_bombs;
    int wetness;
    
    // Static properties (from initialization)
    int shoot_cooldown;
    int optimal_range;
    int soaking_power;
    int max_splash_bombs;
    
    bool is_alive() const {
        return wetness < MAX_WETNESS;
    }
    
    bool can_shoot() const {
        return cooldown == 0 && is_alive();
    }
    
    bool can_throw() const {
        return splash_bombs > 0 && is_alive();
    }
    
    // Distance penalty for territory control when wetness >= 50
    int effective_distance(const Pos& target) const {
        int base_dist = pos.manhattan_distance(target);
        return (wetness >= 50) ? base_dist * 2 : base_dist;
    }
};

// Tile types
enum TileType {
    EMPTY = 0,
    LOW_COVER = 1,
    HIGH_COVER = 2
};

// Grid tile
struct Tile {
    Pos pos;
    TileType type;
    
    bool is_passable() const {
        return type == EMPTY;
    }
    
    double cover_reduction() const {
        switch (type) {
            case LOW_COVER: return 0.5;
            case HIGH_COVER: return 0.25;
            default: return 1.0;
        }
    }
};

// Action types
enum ActionType {
    MOVE,
    SHOOT,
    THROW,
    HUNKER_DOWN
};

// Action structure
struct Action {
    ActionType type;
    int agent_id;
    Pos target_pos;  // For MOVE and THROW
    int target_agent_id;  // For SHOOT
    
    Action() : type(HUNKER_DOWN), agent_id(-1), target_agent_id(-1) {}
    
    static Action move(int agent_id, Pos pos) {
        Action a;
        a.type = MOVE;
        a.agent_id = agent_id;
        a.target_pos = pos;
        return a;
    }
    
    static Action shoot(int agent_id, int target_id) {
        Action a;
        a.type = SHOOT;
        a.agent_id = agent_id;
        a.target_agent_id = target_id;
        return a;
    }
    
    static Action throw_bomb(int agent_id, Pos pos) {
        Action a;
        a.type = THROW;
        a.agent_id = agent_id;
        a.target_pos = pos;
        return a;
    }
    
    static Action hunker(int agent_id) {
        Action a;
        a.type = HUNKER_DOWN;
        a.agent_id = agent_id;
        return a;
    }
    
    string to_string() const {
        stringstream ss;
        ss << agent_id << ";";
        
        switch (type) {
            case MOVE:
                ss << "MOVE " << target_pos.x << " " << target_pos.y;
                break;
            case SHOOT:
                ss << "SHOOT " << target_agent_id;
                break;
            case THROW:
                ss << "THROW " << target_pos.x << " " << target_pos.y;
                break;
            case HUNKER_DOWN:
                ss << "HUNKER_DOWN";
                break;
        }
        
        return ss.str();
    }
};

// Forward declarations
class GameState;
class MCTSNode;

class GameState {
public:
    vector<Agent> agents;
    vector<vector<Tile>> grid;
    int width, height;
    int my_player_id;
    int turn;
    vector<int> player_scores;

    GameState() : width(0), height(0), my_player_id(0), turn(0) {
        player_scores.resize(2, 0);
    }

    void parse_initialization() {
        cin >> my_player_id;

        int agent_data_count;
        cin >> agent_data_count;

        agents.resize(agent_data_count);
        for (int i = 0; i < agent_data_count; i++) {
            cin >> agents[i].id >> agents[i].player >> agents[i].shoot_cooldown
                >> agents[i].optimal_range >> agents[i].soaking_power >> agents[i].max_splash_bombs;
            agents[i].splash_bombs = agents[i].max_splash_bombs;
            agents[i].wetness = 0;
            agents[i].cooldown = 0;
        }

        cin >> width >> height;
        grid.resize(height, vector<Tile>(width));

        for (int i = 0; i < height; i++) {
            for (int j = 0; j < width; j++) {
                int x, y, tile_type;
                cin >> x >> y >> tile_type;
                grid[i][j].pos = Pos(x, y);
                grid[i][j].type = static_cast<TileType>(tile_type);
            }
        }
    }

    void parse_turn_input() {
        int agent_count;
        cin >> agent_count;

        // Update agent positions and states
        for (int i = 0; i < agent_count; i++) {
            int agent_id, x, y, cooldown, splash_bombs, wetness;
            cin >> agent_id >> x >> y >> cooldown >> splash_bombs >> wetness;

            // Find and update the agent
            for (auto& agent : agents) {
                if (agent.id == agent_id) {
                    agent.pos = Pos(x, y);
                    agent.cooldown = cooldown;
                    agent.splash_bombs = splash_bombs;
                    agent.wetness = wetness;
                    break;
                }
            }
        }

        int my_agent_count;
        cin >> my_agent_count;
    }

    GameState* copy() const {
        GameState* new_state = new GameState();
        new_state->agents = agents;
        new_state->grid = grid;
        new_state->width = width;
        new_state->height = height;
        new_state->my_player_id = my_player_id;
        new_state->turn = turn;
        new_state->player_scores = player_scores;
        return new_state;
    }

    vector<Agent> get_my_agents() const {
        vector<Agent> my_agents;
        for (const auto& agent : agents) {
            if (agent.player == my_player_id && agent.is_alive()) {
                my_agents.push_back(agent);
            }
        }
        return my_agents;
    }

    vector<Agent> get_enemy_agents() const {
        vector<Agent> enemy_agents;
        for (const auto& agent : agents) {
            if (agent.player != my_player_id && agent.is_alive()) {
                enemy_agents.push_back(agent);
            }
        }
        return enemy_agents;
    }

    bool is_valid_position(const Pos& pos) const {
        return pos.x >= 0 && pos.x < width && pos.y >= 0 && pos.y < height;
    }

    bool is_passable(const Pos& pos) const {
        if (!is_valid_position(pos)) return false;
        return grid[pos.y][pos.x].is_passable();
    }

    bool is_occupied(const Pos& pos) const {
        for (const auto& agent : agents) {
            if (agent.is_alive() && agent.pos == pos) {
                return true;
            }
        }
        return false;
    }

    Agent* get_agent_by_id(int id) {
        for (auto& agent : agents) {
            if (agent.id == id) {
                return &agent;
            }
        }
        return nullptr;
    }

    // Get adjacent positions (orthogonal)
    vector<Pos> get_adjacent_positions(const Pos& pos) const {
        vector<Pos> adjacent;
        vector<pair<int, int>> directions = {{0, 1}, {0, -1}, {1, 0}, {-1, 0}};

        for (const auto& dir : directions) {
            Pos new_pos(pos.x + dir.first, pos.y + dir.second);
            if (is_valid_position(new_pos)) {
                adjacent.push_back(new_pos);
            }
        }
        return adjacent;
    }

    // Get all positions within splash range (8-directional)
    vector<Pos> get_splash_positions(const Pos& center) const {
        vector<Pos> splash_positions;
        for (int dx = -1; dx <= 1; dx++) {
            for (int dy = -1; dy <= 1; dy++) {
                Pos pos(center.x + dx, center.y + dy);
                if (is_valid_position(pos)) {
                    splash_positions.push_back(pos);
                }
            }
        }
        return splash_positions;
    }

    // Calculate shooting damage with range and cover modifiers
    int calculate_shoot_damage(const Agent& shooter, const Agent& target) const {
        int distance = shooter.pos.manhattan_distance(target.pos);

        // Range modifier
        double range_modifier = 0.0;
        if (distance <= shooter.optimal_range) {
            range_modifier = 1.0;
        } else if (distance <= 2 * shooter.optimal_range) {
            range_modifier = 0.5;
        } else {
            return 0; // Too far to shoot
        }

        // Cover modifier (simplified - assumes no cover for now)
        double cover_modifier = 1.0;

        // Hunker modifier (simplified - assumes no hunkering for now)
        double hunker_modifier = 1.0;

        return static_cast<int>(shooter.soaking_power * range_modifier * cover_modifier * hunker_modifier);
    }

    // Apply a single action to the game state
    void apply_single_action(const Action& action) {
        Agent* agent = get_agent_by_id(action.agent_id);
        if (!agent || !agent->is_alive()) return;

        switch (action.type) {
            case MOVE: {
                // Simple movement - just move to adjacent position if valid
                if (is_passable(action.target_pos) && !is_occupied(action.target_pos)) {
                    int distance = agent->pos.manhattan_distance(action.target_pos);
                    if (distance == 1) { // Only allow adjacent moves for simplicity
                        agent->pos = action.target_pos;
                    }
                }
                break;
            }
            case SHOOT: {
                if (agent->can_shoot()) {
                    Agent* target = get_agent_by_id(action.target_agent_id);
                    if (target && target->is_alive() && target->player != agent->player) {
                        int damage = calculate_shoot_damage(*agent, *target);
                        if (damage > 0) {
                            target->wetness += damage;
                            agent->cooldown = agent->shoot_cooldown + 1;
                        }
                    }
                }
                break;
            }
            case THROW: {
                if (agent->can_throw() && agent->pos.manhattan_distance(action.target_pos) <= MAX_THROW_RANGE) {
                    vector<Pos> splash_positions = get_splash_positions(action.target_pos);
                    for (const Pos& pos : splash_positions) {
                        for (auto& target_agent : agents) {
                            if (target_agent.is_alive() && target_agent.pos == pos) {
                                target_agent.wetness += SPLASH_DAMAGE;
                            }
                        }
                    }
                    agent->splash_bombs--;
                }
                break;
            }
            case HUNKER_DOWN:
                // Hunkering implemented as no-op for simplicity
                break;
        }
    }

    // Apply all actions for a turn
    void apply_actions(const vector<Action>& actions) {
        // Decrease cooldowns
        for (auto& agent : agents) {
            if (agent.cooldown > 0) {
                agent.cooldown--;
            }
        }

        // Apply actions in order: MOVE, HUNKER_DOWN, SHOOT/THROW
        vector<Action> move_actions, combat_actions;

        for (const Action& action : actions) {
            if (action.type == MOVE) {
                move_actions.push_back(action);
            } else {
                combat_actions.push_back(action);
            }
        }

        // Apply moves first
        for (const Action& action : move_actions) {
            apply_single_action(action);
        }

        // Apply combat actions
        for (const Action& action : combat_actions) {
            apply_single_action(action);
        }

        // Remove dead agents
        agents.erase(remove_if(agents.begin(), agents.end(),
                              [](const Agent& a) { return !a.is_alive(); }),
                    agents.end());

        turn++;
    }

    // Update territory control and calculate scores
    void update_territory_control() {
        player_scores[0] = 0;
        player_scores[1] = 0;

        vector<int> territory_control(2, 0);

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                Pos tile_pos(x, y);
                int closest_distance[2] = {INT_MAX, INT_MAX};

                // Find closest agent for each player
                for (const auto& agent : agents) {
                    if (agent.is_alive()) {
                        int distance = agent.effective_distance(tile_pos);
                        if (distance < closest_distance[agent.player]) {
                            closest_distance[agent.player] = distance;
                        }
                    }
                }

                // Assign territory control
                if (closest_distance[0] < closest_distance[1]) {
                    territory_control[0]++;
                } else if (closest_distance[1] < closest_distance[0]) {
                    territory_control[1]++;
                }
            }
        }

        // Score points based on territory advantage
        // Award points equal to the difference when controlling more tiles
        int diff = territory_control[my_player_id] - territory_control[1 - my_player_id];
        if (diff > 0) {
            player_scores[my_player_id] += diff;
        } else if (diff < 0) {
            player_scores[1 - my_player_id] += (-diff);
        }
    }

    // Calculate wetness concentration score (prefer eliminating agents)
    double calculate_wetness_concentration_score() const {
        double my_score = 0.0, enemy_score = 0.0;

        for (const auto& agent : agents) {
            if (!agent.is_alive()) continue;

            double wetness_score = 0.0;
            if (agent.wetness >= 80) {
                wetness_score = 100.0; // Almost eliminated
            } else if (agent.wetness >= 60) {
                wetness_score = 50.0;  // Heavily damaged
            } else if (agent.wetness >= 40) {
                wetness_score = 20.0;  // Moderately damaged
            } else {
                wetness_score = agent.wetness * 0.1; // Light damage
            }

            if (agent.player == my_player_id) {
                enemy_score += wetness_score; // Enemy damaging us is bad
            } else {
                my_score += wetness_score; // Us damaging enemy is good
            }
        }

        return my_score - enemy_score;
    }

    // Evaluate the current game state score
    double evaluate_score() const {
        // Territory control is the primary factor
        double territory_score = player_scores[my_player_id] - player_scores[1 - my_player_id];

        // Wetness concentration is secondary
        double wetness_score = calculate_wetness_concentration_score();

        // Agent count advantage
        vector<Agent> my_agents = get_my_agents();
        vector<Agent> enemy_agents = get_enemy_agents();
        double agent_count_score = (my_agents.size() - enemy_agents.size()) * 50.0;

        // Formation quality for territory control
        double formation_score = 0.0;
        if (is_formation_optimal_for_territory()) {
            formation_score = 30.0;
        }

        // Positional advantage score
        double positional_score = 0.0;
        for (const auto& agent : my_agents) {
            positional_score += calculate_territorial_value(agent.pos);
        }
        for (const auto& agent : enemy_agents) {
            positional_score -= calculate_territorial_value(agent.pos) * 0.5; // Enemy positions hurt us less
        }

        // Dynamic weight adjustment based on game state
        double territory_weight = 15.0; // Increased from 10.0
        double wetness_weight = 3.0;    // Increased from 2.0
        double agent_weight = 75.0;     // Increased from 50.0
        double formation_weight = 1.0;
        double position_weight = 0.5;

        // Adjust weights based on game phase
        int total_turns_estimated = 100;
        double game_progress = static_cast<double>(turn) / total_turns_estimated;

        if (game_progress < 0.3) {
            // Early game - prioritize territory expansion
            territory_weight *= 1.5;
            formation_weight *= 2.0;
            position_weight *= 2.0;
        } else if (game_progress > 0.7) {
            // Late game - prioritize eliminating enemies if behind
            if (territory_score < 0) {
                wetness_weight *= 2.0;
                agent_weight *= 1.5;
            }
        }

        // Adjust weights based on current situation
        if (my_agents.size() < enemy_agents.size()) {
            // We're outnumbered - focus on territory and positioning
            territory_weight *= 1.3;
            position_weight *= 1.5;
        } else if (my_agents.size() > enemy_agents.size()) {
            // We have advantage - be more aggressive
            wetness_weight *= 1.2;
        }

        // Combine scores with dynamic weights
        return territory_score * territory_weight +
               wetness_score * wetness_weight +
               agent_count_score * agent_weight +
               formation_score * formation_weight +
               positional_score * position_weight;
    }

    // Calculate territorial value of a position
    double calculate_territorial_value(const Pos& pos) const {
        double value = 0.0;

        // Central positions are more valuable for territory control
        double center_x = width / 2.0;
        double center_y = height / 2.0;
        double distance_to_center = sqrt(pow(pos.x - center_x, 2) + pow(pos.y - center_y, 2));
        double max_distance = sqrt(pow(center_x, 2) + pow(center_y, 2));
        value += (1.0 - distance_to_center / max_distance) * 50.0; // 0-50 points for centrality

        // Positions that threaten enemy territory are valuable
        vector<Agent> enemy_agents = get_enemy_agents();
        if (!enemy_agents.empty()) {
            double min_enemy_distance = numeric_limits<double>::max();
            for (const auto& enemy : enemy_agents) {
                double dist = pos.manhattan_distance(enemy.pos);
                min_enemy_distance = min(min_enemy_distance, dist);
            }
            // Closer to enemies = more territorial pressure (but not too close)
            if (min_enemy_distance >= 2 && min_enemy_distance <= 6) {
                value += (8.0 - min_enemy_distance) * 10.0; // 20-60 points for optimal distance
            }
        }

        // Positions near cover are valuable for sustainability
        for (int dx = -1; dx <= 1; dx++) {
            for (int dy = -1; dy <= 1; dy++) {
                Pos check_pos(pos.x + dx, pos.y + dy);
                if (is_valid_position(check_pos)) {
                    TileType tile_type = grid[check_pos.y][check_pos.x].type;
                    if (tile_type == LOW_COVER) {
                        value += 15.0;
                    } else if (tile_type == HIGH_COVER) {
                        value += 25.0;
                    }
                }
            }
        }

        return value;
    }

    // Calculate how many tiles this position would help control
    int calculate_territory_influence(const Pos& pos, int player_id) const {
        int influenced_tiles = 0;

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                Pos tile_pos(x, y);
                int closest_distance[2] = {INT_MAX, INT_MAX};

                // Find closest agent for each player, considering this new position
                for (const auto& agent : agents) {
                    if (agent.is_alive()) {
                        int distance = agent.effective_distance(tile_pos);
                        if (distance < closest_distance[agent.player]) {
                            closest_distance[agent.player] = distance;
                        }
                    }
                }

                // Consider the new position
                int new_distance = pos.manhattan_distance(tile_pos);
                if (new_distance < closest_distance[player_id]) {
                    closest_distance[player_id] = new_distance;
                }

                // Check if this position would help control this tile
                if (closest_distance[player_id] < closest_distance[1 - player_id]) {
                    influenced_tiles++;
                }
            }
        }

        return influenced_tiles;
    }

    // Find the best territorial position for an agent
    Pos find_best_territorial_position(const Agent& agent) const {
        vector<Pos> adjacent = get_adjacent_positions(agent.pos);
        Pos best_pos = agent.pos;
        double best_score = calculate_territorial_value(agent.pos);

        for (const Pos& pos : adjacent) {
            if (is_passable(pos) && !is_occupied(pos)) {
                double territorial_value = calculate_territorial_value(pos);
                int territory_influence = calculate_territory_influence(pos, agent.player);
                double total_score = territorial_value + territory_influence * 2.0;

                if (total_score > best_score) {
                    best_score = total_score;
                    best_pos = pos;
                }
            }
        }

        return best_pos;
    }

    // Calculate formation center for coordinated positioning
    Pos calculate_formation_center() const {
        vector<Agent> my_agents = get_my_agents();
        if (my_agents.empty()) {
            return Pos(width / 2, height / 2);
        }

        double center_x = 0.0, center_y = 0.0;
        for (const auto& agent : my_agents) {
            center_x += agent.pos.x;
            center_y += agent.pos.y;
        }
        center_x /= my_agents.size();
        center_y /= my_agents.size();

        return Pos(static_cast<int>(center_x), static_cast<int>(center_y));
    }

    // Calculate ideal formation spread for territory control
    double calculate_ideal_formation_spread() const {
        // Ideal spread depends on map size and enemy positions
        double map_diagonal = sqrt(width * width + height * height);
        vector<Agent> enemy_agents = get_enemy_agents();

        if (enemy_agents.empty()) {
            return map_diagonal * 0.3; // Spread out when no enemies
        }

        // Calculate enemy spread
        double enemy_center_x = 0.0, enemy_center_y = 0.0;
        for (const auto& enemy : enemy_agents) {
            enemy_center_x += enemy.pos.x;
            enemy_center_y += enemy.pos.y;
        }
        enemy_center_x /= enemy_agents.size();
        enemy_center_y /= enemy_agents.size();

        double enemy_spread = 0.0;
        for (const auto& enemy : enemy_agents) {
            double dist = sqrt(pow(enemy.pos.x - enemy_center_x, 2) + pow(enemy.pos.y - enemy_center_y, 2));
            enemy_spread = max(enemy_spread, dist);
        }

        // Match enemy spread but maintain minimum cohesion
        return max(3.0, min(enemy_spread * 1.2, map_diagonal * 0.4));
    }

    // Evaluate if agents are in good formation for territory control
    bool is_formation_optimal_for_territory() const {
        vector<Agent> my_agents = get_my_agents();
        if (my_agents.size() <= 1) return true;

        Pos formation_center = calculate_formation_center();
        double ideal_spread = calculate_ideal_formation_spread();

        // Check if agents are reasonably spread for territory control
        int agents_in_good_position = 0;
        for (const auto& agent : my_agents) {
            double dist_from_center = formation_center.manhattan_distance(agent.pos);
            if (dist_from_center >= ideal_spread * 0.5 && dist_from_center <= ideal_spread * 1.5) {
                agents_in_good_position++;
            }
        }

        return agents_in_good_position >= my_agents.size() * 0.6; // 60% in good position
    }

    // Find coordinated positions for territorial control
    vector<Pos> find_coordinated_territorial_positions() const {
        vector<Agent> my_agents = get_my_agents();
        vector<Pos> target_positions;

        if (my_agents.empty()) return target_positions;

        // Calculate strategic positions based on map control
        vector<Agent> enemy_agents = get_enemy_agents();

        if (enemy_agents.empty()) {
            // No enemies - spread out for maximum territory control
            for (size_t i = 0; i < my_agents.size(); i++) {
                int target_x = (width / (my_agents.size() + 1)) * (i + 1);
                int target_y = height / 2;
                target_positions.push_back(Pos(target_x, target_y));
            }
        } else {
            // Enemies present - form a line or arc facing them
            double enemy_center_x = 0.0, enemy_center_y = 0.0;
            for (const auto& enemy : enemy_agents) {
                enemy_center_x += enemy.pos.x;
                enemy_center_y += enemy.pos.y;
            }
            enemy_center_x /= enemy_agents.size();
            enemy_center_y /= enemy_agents.size();

            // Create positions in a defensive arc
            double formation_distance = 4.0; // Distance from enemy center
            for (size_t i = 0; i < my_agents.size(); i++) {
                double angle = (i - my_agents.size() / 2.0) * 0.5; // Spread agents in arc
                int target_x = static_cast<int>(enemy_center_x + formation_distance * cos(angle));
                int target_y = static_cast<int>(enemy_center_y + formation_distance * sin(angle));

                // Ensure position is valid
                target_x = max(0, min(width - 1, target_x));
                target_y = max(0, min(height - 1, target_y));

                target_positions.push_back(Pos(target_x, target_y));
            }
        }

        return target_positions;
    }

    // Check if game is over
    bool is_game_over() const {
        vector<Agent> my_agents = get_my_agents();
        vector<Agent> enemy_agents = get_enemy_agents();

        // All agents of one side eliminated
        if (my_agents.empty() || enemy_agents.empty()) {
            return true;
        }

        // Point difference too large
        int point_diff = abs(player_scores[0] - player_scores[1]);
        if (point_diff >= MAX_POINT_DIFF) {
            return true;
        }

        // Max turns reached
        if (turn >= MAX_TURNS) {
            return true;
        }

        return false;
    }

    // Generate possible actions for a single agent
    vector<Action> generate_agent_actions(const Agent& agent) const {
        vector<Action> actions;

        if (!agent.is_alive()) {
            return actions;
        }

        // Always include hunker down as an option
        actions.push_back(Action::hunker(agent.id));

        // Movement actions - prioritize territorial and tactical positions
        vector<Pos> adjacent = get_adjacent_positions(agent.pos);
        vector<pair<Pos, double>> scored_positions;

        for (const Pos& pos : adjacent) {
            if (is_passable(pos) && !is_occupied(pos)) {
                double score = 0.0;

                // Territory control value
                score += calculate_territorial_value(pos);

                // Territory influence
                score += calculate_territory_influence(pos, agent.player) * 2.0;

                // Formation maintenance - stay reasonably close to other agents
                vector<Agent> my_agents = get_my_agents();
                if (my_agents.size() > 1) {
                    double avg_distance_to_allies = 0.0;
                    int ally_count = 0;
                    for (const auto& ally : my_agents) {
                        if (ally.id != agent.id && ally.is_alive()) {
                            avg_distance_to_allies += pos.manhattan_distance(ally.pos);
                            ally_count++;
                        }
                    }
                    if (ally_count > 0) {
                        avg_distance_to_allies /= ally_count;
                        // Prefer positions that maintain reasonable formation (not too far, not too close)
                        if (avg_distance_to_allies >= 2.0 && avg_distance_to_allies <= 5.0) {
                            score += 20.0;
                        } else if (avg_distance_to_allies > 5.0) {
                            score -= (avg_distance_to_allies - 5.0) * 5.0; // Penalty for being too far
                        }
                    }
                }

                // Safety consideration - avoid positions too close to enemies if heavily damaged
                if (agent.wetness >= 50) {
                    vector<Agent> enemy_agents = get_enemy_agents();
                    for (const auto& enemy : enemy_agents) {
                        double dist_to_enemy = pos.manhattan_distance(enemy.pos);
                        if (dist_to_enemy <= 2) {
                            score -= 30.0; // Strong penalty for being too close when damaged
                        }
                    }
                }

                scored_positions.push_back({pos, score});
            }
        }

        // Sort positions by score and add the best ones
        sort(scored_positions.begin(), scored_positions.end(),
             [](const pair<Pos, double>& a, const pair<Pos, double>& b) {
                 return a.second > b.second;
             });

        // Add movement actions for top positions (limit to avoid too many options)
        int max_moves = min(3, static_cast<int>(scored_positions.size()));
        for (int i = 0; i < max_moves; i++) {
            actions.push_back(Action::move(agent.id, scored_positions[i].first));
        }

        // Shooting actions
        if (agent.can_shoot()) {
            for (const auto& target : agents) {
                if (target.is_alive() && target.player != agent.player) {
                    int distance = agent.pos.manhattan_distance(target.pos);
                    if (distance <= 2 * agent.optimal_range) {
                        actions.push_back(Action::shoot(agent.id, target.id));
                    }
                }
            }
        }

        // Throwing actions
        if (agent.can_throw()) {
            // Generate some throw positions (simplified)
            for (int dx = -MAX_THROW_RANGE; dx <= MAX_THROW_RANGE; dx++) {
                for (int dy = -MAX_THROW_RANGE; dy <= MAX_THROW_RANGE; dy++) {
                    if (abs(dx) + abs(dy) <= MAX_THROW_RANGE) {
                        Pos throw_pos(agent.pos.x + dx, agent.pos.y + dy);
                        if (is_valid_position(throw_pos)) {
                            actions.push_back(Action::throw_bomb(agent.id, throw_pos));
                        }
                    }
                }
            }
        }

        return actions;
    }

    // Generate all possible action combinations (optimized for performance)
    vector<vector<Action>> generate_all_action_combinations() {
        vector<vector<Action>> combinations;
        vector<Agent> my_agents = get_my_agents();

        if (my_agents.empty()) {
            return combinations;
        }

        // For performance, limit to a reasonable number of combinations
        // Use a greedy approach to generate good action combinations

        // Strategy 1: Aggressive (prioritize shooting and throwing)
        vector<Action> aggressive_actions;
        for (const auto& agent : my_agents) {
            vector<Action> agent_actions = generate_agent_actions(agent);
            Action best_action = Action::hunker(agent.id);

            // Prioritize shooting, then throwing, then moving
            for (const auto& action : agent_actions) {
                if (action.type == SHOOT) {
                    best_action = action;
                    break;
                } else if (action.type == THROW && best_action.type != SHOOT) {
                    best_action = action;
                } else if (action.type == MOVE && best_action.type == HUNKER_DOWN) {
                    best_action = action;
                }
            }
            aggressive_actions.push_back(best_action);
        }
        if (!aggressive_actions.empty()) {
            combinations.push_back(aggressive_actions);
        }

        // Strategy 2: Defensive (prioritize moving to safety)
        vector<Action> defensive_actions;
        for (const auto& agent : my_agents) {
            vector<Action> agent_actions = generate_agent_actions(agent);
            Action best_action = Action::hunker(agent.id);

            // Prioritize moving if heavily damaged, otherwise shoot
            if (agent.wetness >= 50) {
                for (const auto& action : agent_actions) {
                    if (action.type == MOVE) {
                        best_action = action;
                        break;
                    }
                }
            } else {
                for (const auto& action : agent_actions) {
                    if (action.type == SHOOT) {
                        best_action = action;
                        break;
                    }
                }
            }
            defensive_actions.push_back(best_action);
        }
        if (!defensive_actions.empty()) {
            combinations.push_back(defensive_actions);
        }

        // Strategy 3: Territory Expansion (prioritize territorial control)
        vector<Action> expansion_actions;
        for (const auto& agent : my_agents) {
            vector<Action> agent_actions = generate_agent_actions(agent);
            Action best_action = Action::hunker(agent.id);
            double best_score = -1000.0;

            // Find the action that maximizes territorial value
            for (const auto& action : agent_actions) {
                double score = 0.0;

                if (action.type == MOVE) {
                    score = calculate_territorial_value(action.target_pos);
                    score += calculate_territory_influence(action.target_pos, agent.player) * 3.0;
                } else if (action.type == SHOOT) {
                    // Shooting can help by eliminating threats to our territory
                    Agent* target = get_agent_by_id(action.target_agent_id);
                    if (target && target->wetness >= 70) {
                        score = 50.0; // High value for eliminating near-dead enemies
                    } else {
                        score = 20.0; // Moderate value for damaging enemies
                    }
                } else if (action.type == THROW) {
                    // Throwing can clear multiple enemies from contested areas
                    score = 30.0;
                }

                if (score > best_score) {
                    best_score = score;
                    best_action = action;
                }
            }
            expansion_actions.push_back(best_action);
        }
        if (!expansion_actions.empty()) {
            combinations.push_back(expansion_actions);
        }

        // Strategy 4: Territory Consolidation (strengthen held positions)
        vector<Action> consolidation_actions;
        for (const auto& agent : my_agents) {
            vector<Action> agent_actions = generate_agent_actions(agent);
            Action best_action = Action::hunker(agent.id);

            // If agent is in a good territorial position, prioritize defense
            double current_territorial_value = calculate_territorial_value(agent.pos);

            if (current_territorial_value > 60.0 || agent.wetness >= 40) {
                // Stay and defend or hunker down
                for (const auto& action : agent_actions) {
                    if (action.type == SHOOT) {
                        best_action = action;
                        break;
                    } else if (action.type == HUNKER_DOWN) {
                        best_action = action;
                    }
                }
            } else {
                // Move to better position
                Pos best_pos = find_best_territorial_position(agent);
                if (best_pos != agent.pos) {
                    best_action = Action::move(agent.id, best_pos);
                }
            }
            consolidation_actions.push_back(best_action);
        }
        if (!consolidation_actions.empty()) {
            combinations.push_back(consolidation_actions);
        }

        // Strategy 5: Frontline Strategy (establish forward positions)
        vector<Action> frontline_actions;
        vector<Agent> enemy_agents = get_enemy_agents();
        if (!enemy_agents.empty()) {
            // Calculate enemy center of mass
            double enemy_center_x = 0.0, enemy_center_y = 0.0;
            for (const auto& enemy : enemy_agents) {
                enemy_center_x += enemy.pos.x;
                enemy_center_y += enemy.pos.y;
            }
            enemy_center_x /= enemy_agents.size();
            enemy_center_y /= enemy_agents.size();

            for (const auto& agent : my_agents) {
                vector<Action> agent_actions = generate_agent_actions(agent);
                Action best_action = Action::hunker(agent.id);
                double best_score = -1000.0;

                for (const auto& action : agent_actions) {
                    double score = 0.0;

                    if (action.type == MOVE) {
                        // Prefer positions that are closer to enemy center but not too close
                        double dist_to_enemy_center = sqrt(pow(action.target_pos.x - enemy_center_x, 2) +
                                                         pow(action.target_pos.y - enemy_center_y, 2));
                        if (dist_to_enemy_center >= 3.0 && dist_to_enemy_center <= 6.0) {
                            score = 100.0 - dist_to_enemy_center * 10.0;
                        }
                        score += calculate_territorial_value(action.target_pos) * 0.5;
                    } else if (action.type == SHOOT || action.type == THROW) {
                        score = 40.0; // Combat actions are good for frontline
                    }

                    if (score > best_score) {
                        best_score = score;
                        best_action = action;
                    }
                }
                frontline_actions.push_back(best_action);
            }
        }
        if (!frontline_actions.empty()) {
            combinations.push_back(frontline_actions);
        }

        // Strategy 6: Coordinated Formation (use coordinated positioning)
        vector<Action> formation_actions;
        if (!is_formation_optimal_for_territory()) {
            vector<Pos> target_positions = find_coordinated_territorial_positions();
            for (size_t i = 0; i < my_agents.size() && i < target_positions.size(); i++) {
                const auto& agent = my_agents[i];
                Pos target = target_positions[i];

                // Find best path towards target position
                vector<Pos> adjacent = get_adjacent_positions(agent.pos);
                Pos best_move = agent.pos;
                double best_distance = agent.pos.manhattan_distance(target);

                for (const Pos& pos : adjacent) {
                    if (is_passable(pos) && !is_occupied(pos)) {
                        double distance = pos.manhattan_distance(target);
                        if (distance < best_distance) {
                            best_distance = distance;
                            best_move = pos;
                        }
                    }
                }

                if (best_move != agent.pos) {
                    formation_actions.push_back(Action::move(agent.id, best_move));
                } else {
                    // Can't move towards target, try to shoot or hunker
                    vector<Action> agent_actions = generate_agent_actions(agent);
                    Action best_action = Action::hunker(agent.id);
                    for (const auto& action : agent_actions) {
                        if (action.type == SHOOT) {
                            best_action = action;
                            break;
                        }
                    }
                    formation_actions.push_back(best_action);
                }
            }
        } else {
            // Formation is good, focus on combat
            for (const auto& agent : my_agents) {
                vector<Action> agent_actions = generate_agent_actions(agent);
                Action best_action = Action::hunker(agent.id);
                for (const auto& action : agent_actions) {
                    if (action.type == SHOOT || action.type == THROW) {
                        best_action = action;
                        break;
                    }
                }
                formation_actions.push_back(best_action);
            }
        }
        if (!formation_actions.empty()) {
            combinations.push_back(formation_actions);
        }

        // Strategy 7: Balanced (mix of actions) - keep original as fallback
        vector<Action> balanced_actions;
        for (size_t i = 0; i < my_agents.size(); i++) {
            const auto& agent = my_agents[i];
            vector<Action> agent_actions = generate_agent_actions(agent);

            if (!agent_actions.empty()) {
                // Alternate between different action types
                size_t action_index = i % agent_actions.size();
                balanced_actions.push_back(agent_actions[action_index]);
            } else {
                balanced_actions.push_back(Action::hunker(agent.id));
            }
        }
        if (!balanced_actions.empty()) {
            combinations.push_back(balanced_actions);
        }

        return combinations;
    }
};

// MCTS Node class (defined after GameState to avoid forward declaration issues)
class MCTSNode {
public:
    GameState* state;
    MCTSNode* parent;
    vector<unique_ptr<MCTSNode>> children;
    vector<Action> actions; // Actions for all agents
    vector<vector<Action>> untried_actions; // Actions not yet expanded

    int visits;
    double total_score;
    bool fully_expanded;

    MCTSNode(GameState* state, MCTSNode* parent = nullptr)
        : state(state), parent(parent), visits(0), total_score(0.0), fully_expanded(false) {
        if (state && !state->is_game_over()) {
            untried_actions = state->generate_all_action_combinations();
        }
        fully_expanded = untried_actions.empty();
    }

    ~MCTSNode() {
        delete state;
    }

    double ucb1_value(double exploration_param = sqrt(2.0)) const {
        if (visits == 0) {
            return numeric_limits<double>::infinity();
        }

        double exploitation = total_score / visits;
        double exploration = exploration_param * sqrt(log(parent->visits) / visits);
        return exploitation + exploration;
    }

    MCTSNode* select_child() const {
        MCTSNode* best_child = nullptr;
        double best_value = -numeric_limits<double>::infinity();

        for (const auto& child : children) {
            double value = child->ucb1_value();
            if (value > best_value) {
                best_value = value;
                best_child = child.get();
            }
        }

        return best_child;
    }

    MCTSNode* expand() {
        if (fully_expanded || untried_actions.empty()) {
            return nullptr;
        }

        // Take the first untried action combination
        vector<Action> action_combination = untried_actions.back();
        untried_actions.pop_back();

        if (untried_actions.empty()) {
            fully_expanded = true;
        }

        // Create new state and apply actions
        GameState* new_state = state->copy();
        new_state->apply_actions(action_combination);
        new_state->update_territory_control();

        // Create new child node
        auto child = make_unique<MCTSNode>(new_state, this);
        child->actions = action_combination;

        MCTSNode* child_ptr = child.get();
        children.push_back(move(child));

        return child_ptr;
    }

    double simulate() {
        GameState* sim_state = state->copy();
        random_device rd;
        mt19937 gen(rd());

        // Run random simulation for a few turns
        int sim_turns = 0;
        const int MAX_SIM_TURNS = 5;

        while (!sim_state->is_game_over() && sim_turns < MAX_SIM_TURNS) {
            vector<vector<Action>> possible_actions = sim_state->generate_all_action_combinations();

            if (possible_actions.empty()) {
                break;
            }

            // Choose random action combination
            uniform_int_distribution<> dis(0, possible_actions.size() - 1);
            vector<Action> chosen_actions = possible_actions[dis(gen)];

            sim_state->apply_actions(chosen_actions);
            sim_state->update_territory_control();
            sim_turns++;
        }

        double score = sim_state->evaluate_score();
        delete sim_state;

        return score;
    }

    void backpropagate(double score) {
        visits++;
        total_score += score;

        if (parent) {
            parent->backpropagate(score);
        }
    }

    bool is_terminal() const {
        return state->is_game_over();
    }

    vector<Action> get_best_actions() const {
        if (children.empty()) {
            return actions;
        }

        MCTSNode* best_child = nullptr;
        double best_score = -numeric_limits<double>::infinity();

        for (const auto& child : children) {
            double avg_score = child->visits > 0 ? child->total_score / child->visits : 0.0;
            if (avg_score > best_score) {
                best_score = avg_score;
                best_child = child.get();
            }
        }

        return best_child ? best_child->actions : actions;
    }
};

// MCTS Algorithm class
class MCTS {
private:
    Timer timer;

public:
    // Configure time limit (in milliseconds)
    static void set_time_limit(double limit_ms) {
        TIME_LIMIT_MS = limit_ms;
        cerr << "Time limit set to " << TIME_LIMIT_MS << "ms" << endl;
    }

    vector<Action> search(GameState* root_state) {
        timer.start();

        // Create root node
        MCTSNode root(root_state->copy());

        // If no actions available, return default hunker actions
        if (root.untried_actions.empty() && root.children.empty()) {
            vector<Action> default_actions;
            vector<Agent> my_agents = root_state->get_my_agents();
            for (const auto& agent : my_agents) {
                default_actions.push_back(Action::hunker(agent.id));
            }
            return default_actions;
        }

        int iterations = 0;
        const int MAX_ITERATIONS = 10000;

        while (!timer.time_up() && iterations < MAX_ITERATIONS) {
            MCTSNode* node = &root;

            // Selection phase - traverse down the tree
            while (!node->is_terminal() && node->fully_expanded && !node->children.empty()) {
                node = node->select_child();
                if (!node) break;
            }

            if (!node) break;

            // Expansion phase
            if (!node->is_terminal() && !node->fully_expanded) {
                MCTSNode* expanded = node->expand();
                if (expanded) {
                    node = expanded;
                }
            }

            // Simulation phase
            double score = 0.0;
            try {
                score = node->simulate();
            } catch (const exception& e) {
                // If simulation fails, use current state evaluation
                score = node->state->evaluate_score();
            }

            // Backpropagation phase
            node->backpropagate(score);

            iterations++;

            // Early termination if we have a good enough solution
            if (iterations > 100 && timer.elapsed_ms() > TIME_LIMIT_MS * 0.8) {
                break;
            }
        }

        cerr << "MCTS iterations: " << iterations << ", time: " << timer.elapsed_ms() << "ms" << endl;

        vector<Action> best_actions = root.get_best_actions();

        // Fallback: if no actions found, return hunker actions
        if (best_actions.empty()) {
            vector<Agent> my_agents = root_state->get_my_agents();
            for (const auto& agent : my_agents) {
                best_actions.push_back(Action::hunker(agent.id));
            }
        }

        return best_actions;
    }
};

int main(int argc, char* argv[]) {
    // Parse command line arguments for time configuration
    if (argc > 1) {
        double time_limit = atof(argv[1]);
        if (time_limit > 0 && time_limit <= 50) {
            MCTS::set_time_limit(time_limit);
        } else {
            cerr << "Invalid time limit. Using default: " << TIME_LIMIT_MS << "ms" << endl;
        }
    }

    GameState game_state;
    MCTS mcts;

    // Parse initialization
    game_state.parse_initialization();

    // Main game loop
    while (true) {
        try {
            // Parse turn input
            game_state.parse_turn_input();

            // Run MCTS to find best actions
            vector<Action> best_actions = mcts.search(&game_state);

            // Output actions
            vector<Agent> my_agents = game_state.get_my_agents();

            // Create a map of actions by agent ID
            map<int, Action> action_map;
            for (const Action& action : best_actions) {
                action_map[action.agent_id] = action;
            }

            // Output actions for each agent
            for (const Agent& agent : my_agents) {
                if (action_map.find(agent.id) != action_map.end()) {
                    cout << action_map[agent.id].to_string() << endl;
                } else {
                    // Default to hunker down if no action found
                    cout << Action::hunker(agent.id).to_string() << endl;
                }
            }

        } catch (const exception& e) {
            cerr << "Error: " << e.what() << endl;
            break;
        }
    }

    return 0;
}
